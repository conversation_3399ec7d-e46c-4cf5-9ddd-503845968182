// Modern Authentication JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const togglePasswordBtn = document.getElementById('togglePassword');
    const loginBtn = document.getElementById('loginBtn');
    const loginBtnText = document.getElementById('loginBtnText');
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');

    // Add entrance animation
    document.querySelector('.bg-white\\/70').style.opacity = '0';
    document.querySelector('.bg-white\\/70').style.transform = 'translateY(30px)';

    setTimeout(() => {
        document.querySelector('.bg-white\\/70').style.opacity = '1';
        document.querySelector('.bg-white\\/70').style.transform = 'translateY(0)';
    }, 100);

    // Toggle password visibility with animation
    togglePasswordBtn.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        const icon = this.querySelector('i');
        icon.style.transform = 'scale(0.8)';

        setTimeout(() => {
            if (type === 'password') {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            } else {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            }
            icon.style.transform = 'scale(1)';
        }, 100);
    });

    // Form submission
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Hide any previous error messages
        hideError();
        
        // Show loading state with modern animation
        showLoading();
        
        try {
            const formData = new FormData(loginForm);
            const credentials = {
                username: formData.get('username'),
                password: formData.get('password')
            };

            // Validate inputs
            if (!credentials.username || !credentials.password) {
                throw new Error('Please enter both username and password');
            }

            // Submit login request
            const response = await fetch('/api/admin/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(credentials)
            });

            const result = await response.json();

            if (result.success) {
                // Login successful - redirect to admin dashboard
                showSuccess();
                setTimeout(() => {
                    window.location.href = '/admin';
                }, 1000);
            } else {
                throw new Error(result.message || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            showError(error.message);
        } finally {
            hideLoading();
        }
    });

    // Input validation
    usernameInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.remove('border-red-500');
            this.classList.add('border-green-500');
        }
    });

    passwordInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.remove('border-red-500');
            this.classList.add('border-green-500');
        }
    });

    // Enter key handling
    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loginForm.dispatchEvent(new Event('submit'));
        }
    });

    // Helper functions with modern styling
    function showLoading() {
        loginBtn.disabled = true;
        loginBtnText.innerHTML = '<div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>Signing In...';
        loginBtn.style.opacity = '0.8';
        loginBtn.style.transform = 'scale(0.98)';
    }

    function hideLoading() {
        loginBtn.disabled = false;
        loginBtnText.innerHTML = 'Sign In';
        loginBtn.classList.remove('opacity-75');
    }

    function showSuccess() {
        loginBtnText.innerHTML = '<i class="fas fa-check mr-2"></i>Success!';
        loginBtn.classList.remove('from-blue-600', 'to-purple-600');
        loginBtn.classList.add('from-green-600', 'to-green-700');
    }

    function showError(message) {
        errorText.textContent = message;
        errorMessage.classList.remove('hidden');
        
        // Add shake animation
        errorMessage.classList.add('animate-pulse');
        setTimeout(() => {
            errorMessage.classList.remove('animate-pulse');
        }, 1000);

        // Add red border to inputs
        usernameInput.classList.add('border-red-500');
        passwordInput.classList.add('border-red-500');
    }

    function hideError() {
        errorMessage.classList.add('hidden');
        usernameInput.classList.remove('border-red-500');
        passwordInput.classList.remove('border-red-500');
    }

    // Auto-focus username field
    usernameInput.focus();

    // Add some visual feedback for form interactions
    const inputs = [usernameInput, passwordInput];
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('ring-2', 'ring-blue-200');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('ring-2', 'ring-blue-200');
        });
    });

    // Prevent multiple form submissions
    let isSubmitting = false;
    loginForm.addEventListener('submit', function(e) {
        if (isSubmitting) {
            e.preventDefault();
            return false;
        }
        isSubmitting = true;
        
        // Reset flag after a delay
        setTimeout(() => {
            isSubmitting = false;
        }, 2000);
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Escape key to clear form
        if (e.key === 'Escape') {
            loginForm.reset();
            hideError();
            usernameInput.focus();
        }
    });

    // Add some security features
    
    // Disable right-click context menu on password field
    passwordInput.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });

    // Clear form on page unload for security
    window.addEventListener('beforeunload', function() {
        loginForm.reset();
    });

    // Add visual feedback for caps lock
    passwordInput.addEventListener('keydown', function(e) {
        if (e.getModifierState && e.getModifierState('CapsLock')) {
            showCapsLockWarning();
        } else {
            hideCapsLockWarning();
        }
    });

    function showCapsLockWarning() {
        let warning = document.getElementById('capsLockWarning');
        if (!warning) {
            warning = document.createElement('div');
            warning.id = 'capsLockWarning';
            warning.className = 'text-yellow-600 text-xs mt-1';
            warning.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>Caps Lock is on';
            passwordInput.parentElement.appendChild(warning);
        }
    }

    function hideCapsLockWarning() {
        const warning = document.getElementById('capsLockWarning');
        if (warning) {
            warning.remove();
        }
    }
});
