// Premium Admin Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const exportBtn = document.getElementById('exportBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    const logoutBtn = document.getElementById('logoutBtn');
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const questionsTableBody = document.getElementById('questionsTableBody');
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    const questionModal = document.getElementById('questionModal');
    const closeModal = document.getElementById('closeModal');
    const modalContent = document.getElementById('modalContent');

    // Stats elements
    const totalQuestions = document.getElementById('totalQuestions');
    const pendingQuestions = document.getElementById('pendingQuestions');
    const answeredQuestions = document.getElementById('answeredQuestions');
    const uniqueUsers = document.getElementById('uniqueUsers');

    let allQuestions = [];
    let filteredQuestions = [];

    // Initialize dashboard
    init();

    async function init() {
        try {
            await loadQuestions();
            updateStats();
            renderQuestions();
        } catch (error) {
            console.error('Failed to initialize dashboard:', error);
            showError('Failed to load dashboard data');
        }
    }

    // Load questions from server
    async function loadQuestions() {
        showLoading();
        try {
            const response = await fetch('/api/admin/questions');
            const result = await response.json();
            
            if (result.success) {
                allQuestions = result.questions || [];
                filteredQuestions = [...allQuestions];
            } else {
                throw new Error(result.message || 'Failed to load questions');
            }
        } catch (error) {
            console.error('Error loading questions:', error);
            throw error;
        } finally {
            hideLoading();
        }
    }

    // Update statistics
    function updateStats() {
        const stats = {
            total: allQuestions.length,
            pending: allQuestions.filter(q => q.status === 'pending').length,
            answered: allQuestions.filter(q => q.status === 'answered').length,
            users: new Set(allQuestions.map(q => q.user_email)).size
        };

        animateNumber(totalQuestions, stats.total);
        animateNumber(pendingQuestions, stats.pending);
        animateNumber(answeredQuestions, stats.answered);
        animateNumber(uniqueUsers, stats.users);
    }

    // Animate number counting
    function animateNumber(element, target) {
        const start = parseInt(element.textContent) || 0;
        const duration = 1000;
        const startTime = performance.now();

        function update(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const current = Math.floor(start + (target - start) * progress);
            element.textContent = current;

            if (progress < 1) {
                requestAnimationFrame(update);
            }
        }

        requestAnimationFrame(update);
    }

    // Render questions table
    function renderQuestions() {
        if (filteredQuestions.length === 0) {
            questionsTableBody.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';
        
        questionsTableBody.innerHTML = filteredQuestions.map(question => `
            <tr>
                <td>
                    <div>
                        <strong>${escapeHtml(question.user_name)}</strong><br>
                        <small style="color: var(--color-gray-1);">${escapeHtml(question.user_email)}</small>
                        ${question.user_company ? `<br><small style="color: var(--color-gray-2);">${escapeHtml(question.user_company)}</small>` : ''}
                    </div>
                </td>
                <td>
                    <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;">
                        ${escapeHtml(question.question_text.substring(0, 100))}${question.question_text.length > 100 ? '...' : ''}
                    </div>
                </td>
                <td>
                    <select class="status-badge status-${question.status}" onchange="updateQuestionStatus(${question.id}, this.value)">
                        <option value="pending" ${question.status === 'pending' ? 'selected' : ''}>Pending</option>
                        <option value="reviewed" ${question.status === 'reviewed' ? 'selected' : ''}>Reviewed</option>
                        <option value="answered" ${question.status === 'answered' ? 'selected' : ''}>Answered</option>
                        <option value="archived" ${question.status === 'archived' ? 'selected' : ''}>Archived</option>
                    </select>
                </td>
                <td>
                    <small>${formatDate(question.created_at)}</small>
                </td>
                <td>
                    <button class="action-btn btn-view" onclick="viewQuestion(${question.id})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn btn-delete" onclick="deleteQuestion(${question.id})" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // Filter questions
    function filterQuestions() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;

        filteredQuestions = allQuestions.filter(question => {
            const matchesSearch = !searchTerm || 
                question.user_name.toLowerCase().includes(searchTerm) ||
                question.user_email.toLowerCase().includes(searchTerm) ||
                question.question_text.toLowerCase().includes(searchTerm) ||
                (question.user_company && question.user_company.toLowerCase().includes(searchTerm));

            const matchesStatus = !statusValue || question.status === statusValue;

            return matchesSearch && matchesStatus;
        });

        renderQuestions();
    }

    // Event Listeners
    refreshBtn.addEventListener('click', async () => {
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
        refreshBtn.disabled = true;
        
        try {
            await loadQuestions();
            updateStats();
            renderQuestions();
            showSuccess('Data refreshed successfully');
        } catch (error) {
            showError('Failed to refresh data');
        } finally {
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
            refreshBtn.disabled = false;
        }
    });

    exportBtn.addEventListener('click', async () => {
        exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
        exportBtn.disabled = true;
        
        try {
            const response = await fetch('/api/admin/export');
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `winplus-faq-${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                showSuccess('Export completed successfully');
            } else {
                throw new Error('Export failed');
            }
        } catch (error) {
            showError('Failed to export data');
        } finally {
            exportBtn.innerHTML = '<i class="fas fa-download"></i> Export Excel';
            exportBtn.disabled = false;
        }
    });

    logoutBtn.addEventListener('click', async () => {
        if (confirm('Are you sure you want to logout?')) {
            try {
                await fetch('/api/admin/logout', { method: 'POST' });
                window.location.href = '/admin';
            } catch (error) {
                console.error('Logout error:', error);
                window.location.href = '/admin';
            }
        }
    });

    searchInput.addEventListener('input', debounce(filterQuestions, 300));
    statusFilter.addEventListener('change', filterQuestions);
    
    clearFiltersBtn.addEventListener('click', () => {
        searchInput.value = '';
        statusFilter.value = '';
        filterQuestions();
    });

    closeModal.addEventListener('click', () => {
        questionModal.style.display = 'none';
    });

    questionModal.addEventListener('click', (e) => {
        if (e.target === questionModal) {
            questionModal.style.display = 'none';
        }
    });

    // Global functions for inline event handlers
    window.updateQuestionStatus = async function(questionId, newStatus) {
        try {
            const response = await fetch(`/api/admin/questions/${questionId}/status`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ status: newStatus })
            });

            const result = await response.json();
            if (result.success) {
                const question = allQuestions.find(q => q.id === questionId);
                if (question) {
                    question.status = newStatus;
                    updateStats();
                    filterQuestions();
                }
                showSuccess('Status updated successfully');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            showError('Failed to update status');
            console.error('Error updating status:', error);
        }
    };

    window.viewQuestion = function(questionId) {
        const question = allQuestions.find(q => q.id === questionId);
        if (question) {
            modalContent.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <h5>User Information</h5>
                    <p><strong>Name:</strong> ${escapeHtml(question.user_name)}</p>
                    <p><strong>Email:</strong> ${escapeHtml(question.user_email)}</p>
                    ${question.user_phone ? `<p><strong>Phone:</strong> ${escapeHtml(question.user_phone)}</p>` : ''}
                    ${question.user_company ? `<p><strong>Company:</strong> ${escapeHtml(question.user_company)}</p>` : ''}
                </div>
                <div style="margin-bottom: 20px;">
                    <h5>Question</h5>
                    <p style="background: var(--color-gray-light-1); padding: 15px; border-radius: var(--border-radius-default);">
                        ${escapeHtml(question.question_text)}
                    </p>
                </div>
                <div>
                    <h5>Details</h5>
                    <p><strong>Status:</strong> <span class="status-badge status-${question.status}">${question.status}</span></p>
                    <p><strong>Submitted:</strong> ${formatDate(question.created_at)}</p>
                </div>
            `;
            questionModal.style.display = 'flex';
        }
    };

    window.deleteQuestion = async function(questionId) {
        if (confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
            try {
                const response = await fetch(`/api/admin/questions/${questionId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();
                if (result.success) {
                    allQuestions = allQuestions.filter(q => q.id !== questionId);
                    updateStats();
                    filterQuestions();
                    showSuccess('Question deleted successfully');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('Failed to delete question');
                console.error('Error deleting question:', error);
            }
        }
    };

    // Utility functions
    function showLoading() {
        loadingState.style.display = 'block';
        emptyState.style.display = 'none';
        questionsTableBody.innerHTML = '';
    }

    function hideLoading() {
        loadingState.style.display = 'none';
    }

    function showSuccess(message) {
        // Simple success notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 1001;
            background: #10b981; color: white; padding: 15px 20px;
            border-radius: var(--border-radius-default); box-shadow: var(--box-shadow-strong);
        `;
        notification.innerHTML = `<i class="fas fa-check"></i> ${message}`;
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 3000);
    }

    function showError(message) {
        // Simple error notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 1001;
            background: #ef4444; color: white; padding: 15px 20px;
            border-radius: var(--border-radius-default); box-shadow: var(--box-shadow-strong);
        `;
        notification.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 5000);
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatDate(dateString) {
        return new Date(dateString).toLocaleString();
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});
