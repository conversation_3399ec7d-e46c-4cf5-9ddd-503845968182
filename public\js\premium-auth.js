// Premium Authentication JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const togglePasswordBtn = document.getElementById('togglePassword');
    const loginBtn = document.getElementById('loginBtn');
    const loginBtnText = document.getElementById('loginBtnText');
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');

    // Toggle password visibility
    togglePasswordBtn.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        if (type === 'password') {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        } else {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        }
    });

    // Form submission
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Hide any previous error messages
        hideError();
        
        // Show loading state
        showLoading();
        
        try {
            const formData = new FormData(loginForm);
            const credentials = {
                username: formData.get('username'),
                password: formData.get('password')
            };

            // Validate inputs
            if (!credentials.username || !credentials.password) {
                throw new Error('Please enter both username and password');
            }

            // Submit login request
            const response = await fetch('/api/admin/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(credentials)
            });

            const result = await response.json();

            if (result.success) {
                // Login successful - redirect to admin dashboard
                showSuccess();
                setTimeout(() => {
                    window.location.href = '/admin';
                }, 1000);
            } else {
                throw new Error(result.message || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            showError(error.message);
        } finally {
            hideLoading();
        }
    });

    // Input validation
    usernameInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.style.borderColor = 'var(--color-primary-1)';
        }
    });

    passwordInput.addEventListener('input', function() {
        if (this.value.trim()) {
            this.style.borderColor = 'var(--color-primary-1)';
        }
    });

    // Enter key handling
    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loginForm.dispatchEvent(new Event('submit'));
        }
    });

    // Helper functions
    function showLoading() {
        loginBtn.disabled = true;
        loginBtnText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
        loginBtn.style.opacity = '0.7';
    }

    function hideLoading() {
        loginBtn.disabled = false;
        loginBtnText.innerHTML = 'Sign In';
        loginBtn.style.opacity = '1';
    }

    function showSuccess() {
        loginBtnText.innerHTML = '<i class="fas fa-check"></i> Success!';
        loginBtn.style.background = 'linear-gradient(90deg, #10b981, #059669)';
    }

    function showError(message) {
        errorText.textContent = message;
        errorMessage.style.display = 'block';
        
        // Add error styling to inputs
        usernameInput.style.borderColor = 'var(--color-primary-4)';
        passwordInput.style.borderColor = 'var(--color-primary-4)';
        
        // Shake animation
        errorMessage.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            errorMessage.style.animation = '';
        }, 500);
    }

    function hideError() {
        errorMessage.style.display = 'none';
        usernameInput.style.borderColor = 'var(--color-gray-light-3)';
        passwordInput.style.borderColor = 'var(--color-gray-light-3)';
    }

    // Auto-focus username field
    usernameInput.focus();

    // Add visual feedback for form interactions
    const inputs = [usernameInput, passwordInput];
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.borderColor = 'var(--color-primary-1)';
            this.style.boxShadow = '0 0 0 3px var(--color-primary-light-1)';
        });

        input.addEventListener('blur', function() {
            if (!this.value.trim()) {
                this.style.borderColor = 'var(--color-gray-light-3)';
                this.style.boxShadow = 'none';
            }
        });
    });

    // Prevent multiple form submissions
    let isSubmitting = false;
    loginForm.addEventListener('submit', function(e) {
        if (isSubmitting) {
            e.preventDefault();
            return false;
        }
        isSubmitting = true;
        
        // Reset flag after a delay
        setTimeout(() => {
            isSubmitting = false;
        }, 2000);
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Escape key to clear form
        if (e.key === 'Escape') {
            loginForm.reset();
            hideError();
            usernameInput.focus();
        }
    });

    // Clear form on page unload for security
    window.addEventListener('beforeunload', function() {
        loginForm.reset();
    });

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .form-control:focus {
            transform: translateY(-1px);
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .card {
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);
});
