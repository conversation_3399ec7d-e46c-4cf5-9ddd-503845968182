import Form9 from "@/components/blog/commentForm/Form9";
import Comments from "@/components/blog/Comments";
import Widget2 from "@/components/blog/widgets/Widget2";
import AnimatedText from "@/components/common/AnimatedText";
import Footer9 from "@/components/footers/Footer9";
import Header10 from "@/components/headers/Header10";
import Image from "next/image";
import { strongMultiPages, strongMultiPagesDark } from "@/data/menu";
import { allBlogs } from "@/data/blogs";
import ParallaxContainer from "@/components/common/ParallaxContainer";

export const metadata = {
  title:
    "Home 10 Blog Single Dark || Resonance &mdash; One & Multi Page React Nextjs Creative Template",
  description:
    "Resonance &mdash; One & Multi Page React Nextjs Creative Template",
};
export default async function StrongBlogSinglePageDark(props) {
  const params = await props.params;
  const blog = allBlogs.filter((elm) => elm.id == params.id)[0] || allBlogs[0];
  return (
    <>
      <div className="theme-strong">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar wch-unset">
              <Header10 links={strongMultiPagesDark} />
            </nav>
            <main className="main">
              <ParallaxContainer
                className="page-section bg-dark-alpha-30 light-content parallax-5"
                style={{
                  backgroundImage:
                    "url(/assets/images/demo-strong/section-bg-1.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-sm-30">
                  {/* Home Section Content */}
                  <div className="home-content text-center">
                    <h1 className="section-title-medium mb-0">
                      <span className="wow charsAnimIn" data-splitting="chars">
                        <AnimatedText text={blog.title || blog.postTitle} />
                      </span>
                    </h1>
                    {/* Author, Categories, Comments */}
                    <div
                      className="blog-item-data mt-30 mt-sm-10 mb-0 wow fadeInUp"
                      data-wow-delay="0.2s"
                    >
                      <div className="d-inline-block me-3">
                        <a href="#">
                          <i className="mi-clock size-16" />
                          <span className="visually-hidden">Date:</span>{" "}
                          December 25
                        </a>
                      </div>
                      <div className="d-inline-block me-3">
                        <a href="#">
                          <i className="mi-user size-16" />
                          <span className="visually-hidden">Author:</span> John
                          Doe
                        </a>
                      </div>
                      <div className="d-inline-block me-3">
                        <i className="mi-folder size-16" />
                        <span className="visually-hidden">Categories:</span>
                        <a href="#">Design</a>, <a href="#">Branding</a>
                      </div>
                      <div className="d-inline-block me-3">
                        <a href="#">
                          <i className="mi-message size-16" /> 5 Comments
                        </a>
                      </div>
                    </div>
                    {/* End Author, Categories, Comments */}
                  </div>
                  {/* End Home Section Content */}
                </div>
              </ParallaxContainer>
              <>
                {/* Section */}
                <section className="page-section bg-dark-1 light-content">
                  <div className="container relative">
                    <div className="row">
                      {/* Content */}
                      <div className="col-lg-10 offset-lg-1 col-xl-8 offset-xl-2">
                        {/* Post */}
                        <div className="blog-item mb-80 mb-xs-40">
                          <div className="blog-item-body">
                            <div className="mb-40 mb-xs-30">
                              <Image
                                src="/assets/images/demo-bold/portfolio/4.jpg"
                                alt="Image Description"
                                width={1200}
                                height={819}
                              />
                            </div>
                            <p>
                              Morbi lacus massa, euismod ut turpis molestie,
                              tristique sodales est. Integer sit amet mi id
                              sapien tempor molestie in nec massa. Fusce non
                              ante sed lorem rutrum feugiat. Lorem ipsum dolor
                              sit amet, consectetur adipiscing elit. Mauris non
                              laoreet dui. Morbi lacus massa, euismod ut turpis
                              molestie, tristique sodales est. Integer sit amet
                              mi id sapien tempor molestie in nec massa.
                            </p>
                            <p>
                              Fusce non ante sed lorem rutrum feugiat.
                              Vestibulum pellentesque, purus ut&nbsp;dignissim
                              consectetur, nulla erat ultrices purus,
                              ut&nbsp;consequat sem elit non sem. Morbi lacus
                              massa, euismod ut turpis molestie, tristique
                              sodales est. Integer sit amet mi id sapien tempor
                              molestie in nec massa. Fusce non ante sed lorem
                              rutrum feugiat.
                            </p>
                            <blockquote>
                              <p>
                                Lorem ipsum dolor sit amet, consectetur
                                adipiscing elit. Integer posuere erat
                                a&nbsp;ante. Vestibulum pellentesque, purus ut
                                dignissim consectetur, nulla erat ultrices
                                purus.
                              </p>
                              <footer>
                                Someone famous in
                                <cite title="Source Title"> Source Title </cite>
                              </footer>
                            </blockquote>
                            <p>
                              Praesent ultricies ut ipsum non laoreet. Nunc ac
                              <a href="#">ultricies</a> leo. Nulla ac ultrices
                              arcu. Nullam adipiscing lacus in consectetur
                              posuere. Nunc malesuada tellus turpis, ac pretium
                              orci molestie vel. Morbi lacus massa, euismod ut
                              turpis molestie, tristique sodales est. Integer
                              sit amet mi id sapien tempor molestie in nec
                              massa. Fusce non ante sed lorem rutrum feugiat.
                            </p>
                            <ul>
                              <li>First item of the list</li>
                              <li>Second item of the list</li>
                              <li>Third item of the list</li>
                            </ul>
                            <p>
                              Lorem ipsum dolor sit amet, consectetur adipiscing
                              elit. Mauris non laoreet dui. Morbi lacus massa,
                              euismod ut turpis molestie, tristique sodales est.
                              Integer sit amet mi id sapien tempor molestie in
                              nec massa. Fusce non ante sed lorem rutrum
                              feugiat. Vestibulum pellentesque, purus
                              ut&nbsp;dignissim consectetur, nulla erat ultrices
                              purus, ut&nbsp;consequat sem elit non sem.
                            </p>
                          </div>
                        </div>
                        {/* End Post */}
                        {/* Comments */}
                        <div className="mb-80 mb-xs-40">
                          <h4 className="blog-page-title">
                            Comments <small className="number">(3)</small>
                          </h4>
                          <ul className="media-list comment-list clearlist">
                            <Comments />
                            {/* End Comment Item */}
                          </ul>
                        </div>
                        {/* End Comments */}
                        {/* Add Comment */}
                        <div className="mb-80 mb-xs-40">
                          <h4 className="blog-page-title">Leave a comment</h4>
                          {/* Form */}
                          <Form9 />
                          {/* End Form */}
                        </div>
                        {/* End Add Comment */}
                        {/* Prev/Next Post */}
                        <div className="clearfix mt-40">
                          <a href="#" className="blog-item-more left">
                            <i className="mi-chevron-left" />
                            &nbsp;Prev post
                          </a>
                          <a href="#" className="blog-item-more right">
                            Next post&nbsp;
                            <i className="mi-chevron-right" />
                          </a>
                        </div>
                        {/* End Prev/Next Post */}
                      </div>
                      {/* End Content */}
                    </div>
                  </div>
                </section>
                {/* End Section */}
                {/* Divider */}
                <hr className="mt-0 mb-0 white" />
                {/* End Divider */}
                {/* Section */}
                <section className="page-section bg-dark-1 light-content">
                  <div className="container relative">
                    <div className="row mt-n60">
                      <Widget2
                        btnClass="btn btn-mod btn-gray btn-medium btn-round form-control mb-xs-10"
                        inputClass="newsletter-field form-control input-md round mb-10"
                      />
                    </div>
                  </div>
                </section>
              </>
            </main>

            <footer className="footer-1 bg-dark-2 light-content">
              <Footer9 />
            </footer>
          </div>{" "}
        </div>
      </div>{" "}
    </>
  );
}
