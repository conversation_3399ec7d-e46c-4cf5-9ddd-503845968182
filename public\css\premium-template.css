/* Winplus FAQ - Premium Template Design */

/* Import Premium Template Fonts */
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

/* Premium Template Variables */
:root {
  --font-global: "DM Sans", sans-serif;
  --font-alt: "DM Sans", sans-serif;
  --font-serif: Georgia, "Times New Roman", Times, serif;
  --full-wrapper-margin-x: 30px;
  --container-width: 1350px;
  --section-padding-y: 120px;
  --menu-bar-height: 85px;
  --menu-bar-height-scrolled: 65px;
  --color-dark-1: #010101;
  --color-dark-2: #171717;
  --color-dark-3: #272727;
  --color-dark-3a: #333;
  --color-dark-4: #555;
  --color-gray-1: #757575;
  --color-gray-2: #888;
  --color-gray-3: #999;
  --color-gray-light-1: #f1f1f1;
  --color-gray-light-2: #f7f7f7;
  --color-gray-light-3: #e5e5e5;
  --color-gray-light-4: #d5d5d5;
  --color-gray-light-5: #ccc;
  --color-gray-light-6: #bbb;
  --color-primary-1: #4567ed;
  --color-primary-1-a: #375ae3;
  --color-primary-light-1: #e3effe;
  --color-primary-light-1-a: #bcd1f1;
  --color-primary-2: #7752e7;
  --color-primary-light-2: #e7defe;
  --color-primary-3: #b947d9;
  --color-primary-light-3: #f7defe;
  --color-primary-4: #e748b1;
  --color-primary-light-4: #ffe1f5;
  --color-secondary-1: #fbe3a1;
  --gradient-primary-1: linear-gradient(90deg, var(--color-primary-4) 0%, var(--color-primary-3) 33%, var(--color-primary-2) 67%, var(--color-primary-1) 100%);
  --border-radius-default: 4px;
  --border-radius-large: 30px;
  --box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.05), 0px 1px 1px 0px rgba(0, 0, 0, 0.03), 0px 3px 5px 0px rgba(0, 0, 0, 0.03);
  --box-shadow-strong: 0px 5px 10px 0px rgba(0, 0, 0, 0.08), 0px 1px 1px 0px rgba(0, 0, 0, 0.06), 0px 3px 5px 0px rgba(0, 0, 0, 0.06);
  --box-shadow-block: 0px 3px 50px 0px rgba(0, 0, 0, 0.05);
  --transition-default: all 0.27s cubic-bezier(0, 0, 0.58, 1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -ms-overflow-style: scrollbar;
}

body {
    font-family: var(--font-global);
    line-height: 1.6;
    color: var(--color-dark-1);
    background: #fff;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Premium Template Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-global);
    font-weight: 700;
    line-height: 1.2;
    margin: 0;
    color: var(--color-dark-1);
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin: 0 0 1rem;
    line-height: 1.7;
    color: var(--color-gray-1);
}

a {
    color: var(--color-primary-1);
    text-decoration: none;
    transition: var(--transition-default);
}

a:hover {
    color: var(--color-primary-1-a);
}

/* Container and Layout */
.container {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 var(--full-wrapper-margin-x);
    width: 100%;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    padding: 0 15px;
    flex: 1;
}

.section {
    padding: var(--section-padding-y) 0;
}

.section-sm {
    padding: 60px 0;
}

.section-lg {
    padding: 150px 0;
}

/* Premium Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 15px 30px;
    border: none;
    border-radius: var(--border-radius-large);
    font-family: var(--font-global);
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-default);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn-primary {
    background: var(--gradient-primary-1);
    color: #fff;
    box-shadow: var(--box-shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-strong);
    color: #fff;
}

.btn-secondary {
    background: #fff;
    color: var(--color-primary-1);
    border: 2px solid var(--color-primary-1);
    box-shadow: var(--box-shadow);
}

.btn-secondary:hover {
    background: var(--color-primary-1);
    color: #fff;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--color-primary-1);
    border: 2px solid var(--color-primary-1);
}

.btn-outline:hover {
    background: var(--color-primary-1);
    color: #fff;
}

.btn-lg {
    padding: 18px 40px;
    font-size: 18px;
}

.btn-sm {
    padding: 10px 20px;
    font-size: 14px;
}

/* Premium Forms */
.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--color-dark-1);
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid var(--color-gray-light-3);
    border-radius: var(--border-radius-default);
    font-family: var(--font-global);
    font-size: 16px;
    color: var(--color-dark-1);
    background: #fff;
    transition: var(--transition-default);
}

.form-control:focus {
    outline: none;
    border-color: var(--color-primary-1);
    box-shadow: 0 0 0 3px var(--color-primary-light-1);
}

.form-control::placeholder {
    color: var(--color-gray-3);
}

textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* Premium Cards */
.card {
    background: #fff;
    border-radius: var(--border-radius-default);
    box-shadow: var(--box-shadow-block);
    overflow: hidden;
    transition: var(--transition-default);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-strong);
}

.card-body {
    padding: 30px;
}

.card-header {
    padding: 20px 30px;
    background: var(--color-gray-light-2);
    border-bottom: 1px solid var(--color-gray-light-3);
}

/* Premium Grid */
.grid {
    display: grid;
    gap: 30px;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Premium Header */
.header {
    background: #fff;
    box-shadow: var(--box-shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: var(--menu-bar-height);
    transition: var(--transition-default);
}

.header.scrolled {
    height: var(--menu-bar-height-scrolled);
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

.navbar-brand {
    font-size: 24px;
    font-weight: 700;
    color: var(--color-dark-1);
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0 15px;
}

.nav-link {
    color: var(--color-dark-1);
    font-weight: 500;
    transition: var(--transition-default);
}

.nav-link:hover {
    color: var(--color-primary-1);
}

/* Premium Hero */
.hero {
    padding: 150px 0 120px;
    background: var(--color-gray-light-2);
    position: relative;
    overflow: hidden;
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: var(--gradient-primary-1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--color-gray-1);
    margin-bottom: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.5rem; }
    
    .container {
        padding: 0 20px;
    }
    
    .section {
        padding: 60px 0;
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
}
