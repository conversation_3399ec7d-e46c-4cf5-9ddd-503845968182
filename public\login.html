<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winplus FAQ - Admin Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        secondary: {
                            500: '#8b5cf6',
                            600: '#7c3aed',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto">
            <!-- Beautiful Login Card -->
            <div class="card-shadow rounded-3xl p-12">
                <!-- Beautiful Header -->
                <div class="text-center mb-12">
                    <div class="feature-icon mx-auto mb-8">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h1 class="text-4xl font-bold text-gradient mb-4">Admin Login</h1>
                    <p class="subtitle">Winplus FAQ Dashboard</p>
                </div>

                <!-- Beautiful Login Form -->
                <form id="loginForm" class="space-y-8">
                    <div>
                        <label for="username" class="form-label">
                            <i class="fas fa-user"></i>Username
                        </label>
                        <input type="text" id="username" name="username" required
                            class="form-input" placeholder="Enter your username">
                    </div>

                    <div>
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i>Password
                        </label>
                        <div class="relative">
                            <input type="password" id="password" name="password" required
                                class="form-input pr-16" placeholder="Enter your password">
                            <button type="button" id="togglePassword"
                                class="absolute right-5 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-blue-500 transition-all hover:scale-110">
                                <i class="fas fa-eye text-lg"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Beautiful Error Message -->
                    <div id="errorMessage" class="hidden bg-red-50 border-2 border-red-200 text-red-700 px-8 py-6 rounded-2xl">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-4 text-xl"></i>
                            <span id="errorText" class="font-medium">Invalid credentials. Please try again.</span>
                        </div>
                    </div>

                    <!-- Beautiful Submit Button -->
                    <button type="submit" id="loginBtn" class="btn-primary w-full">
                        <i class="fas fa-sign-in-alt mr-3"></i>
                        <span id="loginBtnText">Sign In</span>
                    </button>
                </form>

                <!-- Beautiful Back to FAQ -->
                <div class="text-center mt-12 pt-10 border-t border-gray-200">
                    <a href="/" class="text-gradient hover:opacity-80 transition-all font-semibold text-lg">
                        <i class="fas fa-arrow-left mr-3"></i>Back to FAQ
                    </a>
                </div>
            </div>

            <!-- Beautiful Info Card -->
            <div class="mt-10 card-shadow rounded-2xl p-10 text-center">
                <h3 class="font-bold text-gradient mb-6 text-xl">
                    <i class="fas fa-info-circle mr-3"></i>Admin Access
                </h3>
                <p class="subtitle">
                    This dashboard allows administrators to view and manage FAQ submissions,
                    export data, and monitor user questions for the Winplus platform.
                </p>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
</body>
</html>
