<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winplus FAQ - Admin Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        secondary: {
                            500: '#8b5cf6',
                            600: '#7c3aed',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto">
            <!-- Login Card -->
            <div class="card-shadow rounded-2xl p-10">
                <!-- Header -->
                <div class="text-center mb-10">
                    <div class="feature-icon mx-auto mb-6">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-3">Admin Login</h1>
                    <p class="text-gray-600 text-lg">Winplus FAQ Dashboard</p>
                </div>

                <!-- Login Form -->
                <form id="loginForm" class="space-y-8">
                    <div>
                        <label for="username" class="block text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-user mr-2 text-primary-500"></i>Username
                        </label>
                        <input type="text" id="username" name="username" required
                            class="form-input" placeholder="Enter your username">
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-lock mr-2 text-primary-500"></i>Password
                        </label>
                        <div class="relative">
                            <input type="password" id="password" name="password" required
                                class="form-input pr-12" placeholder="Enter your password">
                            <button type="button" id="togglePassword"
                                class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-primary-500 transition-colors">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div id="errorMessage" class="hidden bg-red-50 border-2 border-red-200 text-red-700 px-6 py-4 rounded-xl">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-3 text-lg"></i>
                            <span id="errorText">Invalid credentials. Please try again.</span>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" id="loginBtn" class="btn-primary w-full">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        <span id="loginBtnText">Sign In</span>
                    </button>
                </form>

                <!-- Back to FAQ -->
                <div class="text-center mt-10 pt-8 border-t border-gray-200">
                    <a href="/" class="text-primary-600 hover:text-primary-800 transition-colors font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>Back to FAQ
                    </a>
                </div>
            </div>

            <!-- Info Card -->
            <div class="mt-8 card-shadow rounded-xl p-8 text-center">
                <h3 class="font-semibold text-gray-800 mb-4 text-lg">
                    <i class="fas fa-info-circle mr-2 text-primary-500"></i>Admin Access
                </h3>
                <p class="text-gray-600">
                    This dashboard allows administrators to view and manage FAQ submissions,
                    export data, and monitor user questions for the Winplus platform.
                </p>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
</body>
</html>
