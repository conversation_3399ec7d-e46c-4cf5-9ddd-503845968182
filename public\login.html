<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winplus FAQ - Admin Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        secondary: {
                            500: '#8b5cf6',
                            600: '#7c3aed',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen flex items-center justify-center">
    <div class="container">
        <div class="content-wrapper">
            <div class="max-w-2xl mx-auto">
                <!-- STUNNING Login Card -->
                <div class="card-shadow rounded-3xl p-16">
                    <!-- STUNNING Header -->
                    <div class="text-center mb-16">
                        <div class="feature-icon mx-auto mb-10">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h1 class="wow-title mb-6">Admin Login</h1>
                        <p class="wow-subtitle">Winplus FAQ Dashboard</p>
                    </div>

                <!-- STUNNING Login Form -->
                <form id="loginForm" class="space-y-12">
                    <div>
                        <label for="username" class="form-label text-white text-lg">
                            <i class="fas fa-user"></i>Username
                        </label>
                        <input type="text" id="username" name="username" required
                            class="form-input" placeholder="Enter your username">
                    </div>

                    <div>
                        <label for="password" class="form-label text-white text-lg">
                            <i class="fas fa-lock"></i>Password
                        </label>
                        <div class="relative">
                            <input type="password" id="password" name="password" required
                                class="form-input pr-20" placeholder="Enter your password">
                            <button type="button" id="togglePassword"
                                class="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-all hover:scale-125">
                                <i class="fas fa-eye text-xl"></i>
                            </button>
                        </div>
                    </div>

                    <!-- STUNNING Error Message -->
                    <div id="errorMessage" class="hidden bg-red-500 bg-opacity-20 border-2 border-red-400 text-red-300 px-10 py-8 rounded-3xl backdrop-blur-sm">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-6 text-2xl"></i>
                            <span id="errorText" class="font-semibold text-lg">Invalid credentials. Please try again.</span>
                        </div>
                    </div>

                    <!-- STUNNING Submit Button -->
                    <button type="submit" id="loginBtn" class="btn-primary w-full text-xl py-6">
                        <i class="fas fa-sign-in-alt mr-4"></i>
                        <span id="loginBtnText">Sign In</span>
                    </button>
                </form>

                <!-- STUNNING Back to FAQ -->
                <div class="text-center mt-16 pt-12 border-t border-gray-600">
                    <a href="/" class="text-gradient hover:opacity-80 transition-all font-bold text-xl">
                        <i class="fas fa-arrow-left mr-4"></i>Back to FAQ
                    </a>
                </div>
            </div>

            <!-- STUNNING Info Card -->
            <div class="mt-12 card-shadow rounded-3xl p-12 text-center">
                <h3 class="font-bold text-gradient mb-8 text-2xl">
                    <i class="fas fa-info-circle mr-4"></i>Admin Access
                </h3>
                <p class="subtitle">
                    This dashboard allows administrators to view and manage FAQ submissions,
                    export data, and monitor user questions for the Winplus platform.
                </p>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
</body>
</html>
