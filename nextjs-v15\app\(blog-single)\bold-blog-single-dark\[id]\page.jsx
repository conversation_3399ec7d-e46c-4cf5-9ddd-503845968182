import Form1 from "@/components/blog/commentForm/Form1";
import Comments from "@/components/blog/Comments";
import NewsLetterForm1 from "@/components/blog/newsletterForms/NewsLetterForm1";
import Slider1 from "@/components/blog/sliders/Slider1";
import Widget1 from "@/components/blog/widgets/Widget1";
import AnimatedText from "@/components/common/AnimatedText";
import Footer2 from "@/components/footers/Footer2";
import Header2 from "@/components/headers/Header2";
import { boldMultipage, boldMultipageDark } from "@/data/menu";
import dynamic from "next/dynamic";
import { allBlogs } from "@/data/blogs";

import Link from "next/link";
import React from "react";
import ParallaxContainer from "@/components/common/ParallaxContainer";
export const metadata = {
  title:
    "Bold Blog Single Dark || Resonance &mdash; One & Multi Page React Nextjs Creative Template",
  description:
    "Resonance &mdash; One & Multi Page React Nextjs Creative Template",
};

export default async function BoldBlogSinglePageDark(props) {
  const params = await props.params;
  const blog = allBlogs.filter((elm) => elm.id == params.id)[0] || allBlogs[0];
  return (
    <>
      <div className="theme-bold">
        {" "}
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark dark-mode transparent stick-fixed wow-menubar">
              <Header2 links={boldMultipageDark} />
            </nav>
            <main id="main">
              <ParallaxContainer
                className="page-section bg-dark-alpha-60 light-content parallax-5"
                style={{
                  backgroundImage:
                    "url(/assets/images/full-width-images/section-bg-13.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-sm-40">
                  <div className="text-center">
                    <h1 className="section-title-medium font-alt mb-0">
                      <span
                        className="wow charsAnimInLong-1"
                        data-splitting="chars"
                      >
                        <AnimatedText text={blog.title || blog.postTitle} />
                      </span>
                    </h1>
                    {/* Author, Categories, Comments */}
                    <div
                      className="blog-item-data mt-30 mt-sm-10 mb-0 wow fadeInUp"
                      data-wow-delay="0.2s"
                    >
                      <div className="d-inline-block me-3">
                        <a href="#">
                          <i className="mi-clock size-16" />
                          <span className="visually-hidden">Date:</span>{" "}
                          December 25
                        </a>
                      </div>
                      <div className="d-inline-block me-3">
                        <a href="#">
                          <i className="mi-user size-16" />
                          <span className="visually-hidden">Author:</span> John
                          Doe
                        </a>
                      </div>
                      <div className="d-inline-block me-3">
                        <i className="mi-folder size-16" />
                        <span className="visually-hidden">Categories:</span>
                        <a href="#">Design</a>, <a href="#">Branding</a>
                      </div>
                      <div className="d-inline-block me-3">
                        <a href="#">
                          <i className="mi-message size-16" /> 5 Comments
                        </a>
                      </div>
                    </div>
                    {/* End Author, Categories, Comments */}
                  </div>
                </div>
              </ParallaxContainer>
              <>
                {/* Section */}
                <section className="page-section bg-dark-1 light-content">
                  <div className="container relative">
                    <div className="row">
                      {/* Content */}
                      <div className="col-lg-8 mb-md-80">
                        {/* Post */}
                        <div className="blog-item mb-80 mb-xs-40">
                          <div className="blog-item-body">
                            {/* Media Gallery */}
                            <div className="blog-media mb-40 mb-xs-30">
                              <Slider1 />
                            </div>
                            <p className="lead">
                              Morbi lacus massa, euismod ut turpis molestie,
                              tristique sodales est. Integer sit amet mi id
                              sapien tempor molestie in nec massa. Fusce non
                              ante sed lorem rutrum feugiat.
                            </p>
                            <p>
                              Lorem ipsum dolor sit amet, consectetur adipiscing
                              elit. Mauris non laoreet dui. Morbi lacus massa,
                              euismod ut turpis molestie, tristique sodales est.
                              Integer sit amet mi id sapien tempor molestie in
                              nec massa.
                            </p>
                            <p>
                              Fusce non ante sed lorem rutrum feugiat.
                              Vestibulum pellentesque, purus ut&nbsp;dignissim
                              consectetur, nulla erat ultrices purus,
                              ut&nbsp;consequat sem elit non sem. Morbi lacus
                              massa, euismod ut turpis molestie, tristique
                              sodales est. Integer sit amet mi id sapien tempor
                              molestie in nec massa. Fusce non ante sed lorem
                              rutrum feugiat.
                            </p>
                            <blockquote>
                              <p>
                                Lorem ipsum dolor sit amet, consectetur
                                adipiscing elit. Integer posuere erat
                                a&nbsp;ante. Vestibulum pellentesque, purus ut
                                dignissim consectetur, nulla erat ultrices
                                purus.
                              </p>
                              <footer>
                                Someone famous in
                                <cite title="Source Title"> Source Title </cite>
                              </footer>
                            </blockquote>
                            <p>
                              Praesent ultricies ut ipsum non laoreet. Nunc ac
                              <a href="#">ultricies</a> leo. Nulla ac ultrices
                              arcu. Nullam adipiscing lacus in consectetur
                              posuere. Nunc malesuada tellus turpis, ac pretium
                              orci molestie vel. Morbi lacus massa, euismod ut
                              turpis molestie, tristique sodales est. Integer
                              sit amet mi id sapien tempor molestie in nec
                              massa. Fusce non ante sed lorem rutrum feugiat.
                            </p>
                            <ul>
                              <li>First item of the list</li>
                              <li>Second item of the list</li>
                              <li>Third item of the list</li>
                            </ul>
                            <p>
                              Lorem ipsum dolor sit amet, consectetur adipiscing
                              elit. Mauris non laoreet dui. Morbi lacus massa,
                              euismod ut turpis molestie, tristique sodales est.
                              Integer sit amet mi id sapien tempor molestie in
                              nec massa. Fusce non ante sed lorem rutrum
                              feugiat. Vestibulum pellentesque, purus
                              ut&nbsp;dignissim consectetur, nulla erat ultrices
                              purus, ut&nbsp;consequat sem elit non sem.
                            </p>
                          </div>
                        </div>
                        {/* End Post */}
                        {/* Comments */}
                        <div className="mb-80 mb-xs-40">
                          <h4 className="blog-page-title">
                            Comments <small className="number">(3)</small>
                          </h4>
                          <ul className="media-list comment-list clearlist">
                            <Comments />
                            {/* End Comment Item */}
                          </ul>
                        </div>
                        {/* End Comments */}
                        {/* Add Comment */}
                        <div className="mb-80 mb-xs-40">
                          <h4 className="blog-page-title">Leave a comment</h4>
                          {/* Form */}
                          <Form1 />
                          {/* End Form */}
                        </div>
                        {/* End Add Comment */}
                        {/* Prev/Next Post */}
                        <div className="clearfix mt-40">
                          <a href="#" className="blog-item-more circle left">
                            <i className="mi-chevron-left" />
                            &nbsp;Prev post
                          </a>
                          <a href="#" className="blog-item-more circle right">
                            Next post&nbsp;
                            <i className="mi-chevron-right" />
                          </a>
                        </div>
                        {/* End Prev/Next Post */}
                      </div>
                      {/* End Content */}
                      {/* Sidebar */}
                      <div className="col-lg-4 col-xl-3 offset-xl-1">
                        <Widget1 />
                      </div>
                      {/* End Sidebar */}
                    </div>
                  </div>
                </section>
                {/* End Section */}
                {/* Divider */}
                <hr className="mt-0 mb-0 white opacity-015" />
                {/* End Divider */}
                {/* Newsletter Section */}
                <section className="small-section bg-dark-1 light-content">
                  <div className="container">
                    {/* Newsletter Form */}
                    <div className="row wow fadeInUp">
                      <div className="col-lg-8 offset-lg-2">
                        <h2 className="section-title-tiny font-alt mb-30">
                          <span className="icon-ellipse" /> Subscribe our
                          newsletter
                        </h2>
                        <NewsLetterForm1 />
                      </div>
                    </div>
                    {/* End Newsletter Form */}
                  </div>
                </section>
                {/* End Newsletter Section */}
              </>
            </main>
            <footer className="footer-1 bg-dark-2 light-content">
              <Footer2 />
            </footer>
          </div>{" "}
        </div>
      </div>{" "}
    </>
  );
}
