// Main FAQ Form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('faqForm');
    const addQuestionBtn = document.getElementById('addQuestionBtn');
    const questionsContainer = document.getElementById('questionsContainer');
    const submitBtn = document.getElementById('submitBtn');
    const successMessage = document.getElementById('successMessage');

    let questionCount = 1;

    // Add new question field
    addQuestionBtn.addEventListener('click', function() {
        questionCount++;
        const questionDiv = document.createElement('div');
        questionDiv.className = 'question-item p-8 rounded-2xl relative fade-in';
        questionDiv.innerHTML = `
            <textarea name="question" rows="5" placeholder="Enter your question about Winplus pharmacy management platform..." required
                class="form-input resize-none"></textarea>
            <button type="button" class="remove-question absolute top-4 right-4 text-red-500 hover:text-red-700 transition-all bg-white rounded-full w-10 h-10 flex items-center justify-center shadow-lg hover:shadow-xl hover:scale-110" title="Remove question">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        questionsContainer.appendChild(questionDiv);

        // Add remove functionality
        const removeBtn = questionDiv.querySelector('.remove-question');
        removeBtn.addEventListener('click', function() {
            questionDiv.remove();
            questionCount--;
            updateAddButtonVisibility();
        });

        updateAddButtonVisibility();
        
        // Focus on the new textarea
        const newTextarea = questionDiv.querySelector('textarea');
        newTextarea.focus();
    });

    // Update add button visibility (limit to 10 questions)
    function updateAddButtonVisibility() {
        if (questionCount >= 10) {
            addQuestionBtn.style.display = 'none';
        } else {
            addQuestionBtn.style.display = 'inline-flex';
        }
    }

    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="loading-spinner mr-2"></span>Submitting...';
        submitBtn.disabled = true;
        submitBtn.classList.add('opacity-75');

        try {
            // Collect form data
            const formData = new FormData(form);
            const questions = [];
            
            // Get all question textareas
            const questionTextareas = form.querySelectorAll('textarea[name="question"]');
            questionTextareas.forEach(textarea => {
                if (textarea.value.trim()) {
                    questions.push(textarea.value.trim());
                }
            });

            if (questions.length === 0) {
                throw new Error('Please enter at least one question.');
            }

            const data = {
                user_name: formData.get('userName'),
                user_email: formData.get('userEmail'),
                user_phone: formData.get('userPhone'),
                user_company: formData.get('userCompany'),
                questions: questions
            };

            // Submit to server
            const response = await fetch('/api/questions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                // Show success message
                successMessage.classList.remove('hidden');
                successMessage.scrollIntoView({ behavior: 'smooth' });
                
                // Reset form
                form.reset();
                
                // Reset questions container to single question
                questionsContainer.innerHTML = `
                    <div class="question-item p-8 rounded-2xl">
                        <textarea name="question" rows="5" placeholder="Enter your question about Winplus pharmacy management platform..." required
                            class="form-input resize-none"></textarea>
                    </div>
                `;
                questionCount = 1;
                updateAddButtonVisibility();

                // Hide success message after 10 seconds
                setTimeout(() => {
                    successMessage.classList.add('hidden');
                }, 10000);
            } else {
                throw new Error(result.message || 'Failed to submit questions');
            }
        } catch (error) {
            console.error('Error submitting questions:', error);
            alert('Error: ' + error.message);
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            submitBtn.classList.remove('opacity-75');
        }
    });

    // Auto-resize textareas
    document.addEventListener('input', function(e) {
        if (e.target.tagName === 'TEXTAREA') {
            e.target.style.height = 'auto';
            e.target.style.height = e.target.scrollHeight + 'px';
        }
    });

    // Form validation
    const inputs = form.querySelectorAll('input[required], textarea[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('border-red-500')) {
                validateField(this);
            }
        });
    });

    function validateField(field) {
        const isValid = field.checkValidity();
        
        if (isValid) {
            field.classList.remove('error');
            field.classList.add('border-green-500');
        } else {
            field.classList.remove('border-green-500');
            field.classList.add('error');
        }
    }

    // Email validation
    const emailInput = document.getElementById('userEmail');
    emailInput.addEventListener('input', function() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            this.setCustomValidity('Please enter a valid email address');
        } else {
            this.setCustomValidity('');
        }
    });

    // Phone number formatting (optional)
    const phoneInput = document.getElementById('userPhone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length >= 10) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        }
        this.value = value;
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add some interactive animations
    const questionItems = document.querySelectorAll('.question-item');
    questionItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
