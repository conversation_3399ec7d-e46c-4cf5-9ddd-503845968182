/* Winplus FAQ - Beautiful Modern Design */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.7;
    color: #1e293b;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Beautiful Background Pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Beautiful Gradients */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    position: relative;
    overflow: hidden;
}

.gradient-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.95) 0%,
        rgba(139, 92, 246, 0.95) 50%,
        rgba(236, 72, 153, 0.95) 100%);
    z-index: 1;
}

.gradient-bg::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    animation: float 20s ease-in-out infinite;
    z-index: 1;
}

.gradient-bg > * {
    position: relative;
    z-index: 2;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Beautiful Cards */
.card-shadow {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 32px 64px -12px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-shadow:hover {
    transform: translateY(-8px);
    box-shadow:
        0 40px 80px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Beautiful Question Items */
.question-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9));
    backdrop-filter: blur(10px);
    border: 2px solid rgba(59, 130, 246, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.question-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.6s;
}

.question-item:hover {
    transform: translateY(-6px) scale(1.02);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow:
        0 25px 50px -12px rgba(59, 130, 246, 0.25),
        0 0 0 1px rgba(59, 130, 246, 0.1);
    background: linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(248, 250, 252, 1));
}

.question-item:hover::before {
    left: 100%;
}

/* Beautiful Form Inputs */
.form-input {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #1e293b;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

.form-input::placeholder {
    color: #94a3b8;
    font-weight: 400;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    background: rgba(255, 255, 255, 1);
    box-shadow:
        0 0 0 4px rgba(59, 130, 246, 0.1),
        0 8px 25px -5px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.form-input:hover:not(:focus) {
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
    box-shadow:
        0 8px 15px -3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.form-input:valid:not(:placeholder-shown) {
    border-color: #10b981;
    box-shadow:
        0 0 0 4px rgba(16, 185, 129, 0.1),
        0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

.form-input.error {
    border-color: #ef4444;
    background: rgba(254, 242, 242, 0.95);
    box-shadow:
        0 0 0 4px rgba(239, 68, 68, 0.1),
        0 4px 6px -1px rgba(239, 68, 68, 0.1);
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Beautiful Buttons */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
    color: white;
    padding: 18px 36px;
    border-radius: 16px;
    font-weight: 600;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 10px 25px -5px rgba(59, 130, 246, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    font-family: 'Poppins', sans-serif;
    letter-spacing: 0.5px;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow:
        0 20px 40px -5px rgba(59, 130, 246, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-primary:active {
    transform: translateY(-2px) scale(1.02);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.95);
    color: #3b82f6;
    padding: 14px 28px;
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    font-family: 'Poppins', sans-serif;
    letter-spacing: 0.3px;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    color: white;
    border-color: transparent;
    transform: translateY(-3px) scale(1.03);
    box-shadow:
        0 15px 30px -5px rgba(59, 130, 246, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Beautiful Feature Cards */
.feature-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 24px;
    padding: 32px 24px;
    text-align: center;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 10px 25px -5px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    transform: scaleX(0);
    transition: transform 0.5s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-12px) scale(1.05);
    box-shadow:
        0 30px 60px -12px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 28px;
    color: white;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
    box-shadow:
        0 15px 30px -5px rgba(59, 130, 246, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow:
        0 20px 40px -5px rgba(59, 130, 246, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* Beautiful Success Message */
.success-message {
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    color: white;
    padding: 24px 28px;
    border-radius: 20px;
    box-shadow:
        0 15px 30px -5px rgba(16, 185, 129, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: none;
    position: relative;
    overflow: hidden;
}

.success-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Beautiful Header Styles */
.header-title {
    font-size: 3.5rem;
    font-weight: 800;
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    letter-spacing: -1px;
    line-height: 1.1;
    position: relative;
}

.header-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    border-radius: 2px;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 10px rgba(59, 130, 246, 0.5); }
    to { box-shadow: 0 0 20px rgba(139, 92, 246, 0.8); }
}

/* Beautiful Container Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Beautiful Labels */
.form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-family: 'Poppins', sans-serif;
    letter-spacing: 0.3px;
}

.form-label i {
    margin-right: 8px;
    color: #3b82f6;
    font-size: 16px;
}

/* Beautiful Grid Layouts */
.beautiful-grid {
    display: grid;
    gap: 24px;
}

.beautiful-grid.cols-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.beautiful-grid.cols-3 {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.beautiful-grid.cols-4 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Beautiful Spacing */
.space-y-6 > * + * {
    margin-top: 24px;
}

.space-y-8 > * + * {
    margin-top: 32px;
}

/* Beautiful Text Styles */
.text-gradient {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.subtitle {
    font-size: 1.25rem;
    color: #64748b;
    font-weight: 500;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-title {
        font-size: 2rem;
    }
    
    .feature-card {
        padding: 16px;
    }
    
    .btn-primary {
        padding: 14px 24px;
        font-size: 14px;
    }
    
    .form-input {
        padding: 10px 14px;
        font-size: 14px;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2563eb, #7c3aed);
}

/* Focus Styles for Accessibility */
.form-input:focus,
.btn-primary:focus,
.btn-secondary:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .gradient-bg {
        background: #3b82f6 !important;
    }
    
    .card-shadow {
        box-shadow: none !important;
        border: 1px solid #e5e7eb !important;
    }
}
