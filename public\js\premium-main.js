// Premium FAQ Form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('faqForm');
    const addQuestionBtn = document.getElementById('addQuestionBtn');
    const questionsContainer = document.getElementById('questionsContainer');
    const submitBtn = document.getElementById('submitBtn');
    const successMessage = document.getElementById('successMessage');
    const header = document.querySelector('.header');

    let questionCount = 1;

    // Header scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = header.offsetHeight;
                const targetPosition = target.offsetTop - headerHeight;
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add new question field
    addQuestionBtn.addEventListener('click', function() {
        questionCount++;
        const questionDiv = document.createElement('div');
        questionDiv.className = 'question-item';
        questionDiv.style.marginTop = '15px';
        questionDiv.style.position = 'relative';
        questionDiv.innerHTML = `
            <textarea name="question" rows="5" placeholder="Enter your question about Winplus pharmacy management platform..." required
                class="form-control"></textarea>
            <button type="button" class="remove-question" style="position: absolute; top: 10px; right: 10px; background: var(--color-primary-4); color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        questionsContainer.appendChild(questionDiv);

        // Add remove functionality
        const removeBtn = questionDiv.querySelector('.remove-question');
        removeBtn.addEventListener('click', function() {
            questionDiv.remove();
            questionCount--;
            updateAddButtonVisibility();
        });

        updateAddButtonVisibility();
        
        // Focus on the new textarea
        const newTextarea = questionDiv.querySelector('textarea');
        newTextarea.focus();
    });

    // Update add button visibility (limit to 10 questions)
    function updateAddButtonVisibility() {
        if (questionCount >= 10) {
            addQuestionBtn.style.display = 'none';
        } else {
            addQuestionBtn.style.display = 'inline-flex';
        }
    }

    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
        submitBtn.disabled = true;

        try {
            // Collect form data
            const formData = new FormData(form);
            const questions = [];
            
            // Get all question textareas
            const questionTextareas = form.querySelectorAll('textarea[name="question"]');
            questionTextareas.forEach(textarea => {
                if (textarea.value.trim()) {
                    questions.push(textarea.value.trim());
                }
            });

            if (questions.length === 0) {
                throw new Error('Please enter at least one question.');
            }

            const data = {
                user_name: formData.get('userName'),
                user_email: formData.get('userEmail'),
                user_phone: formData.get('userPhone'),
                user_company: formData.get('userCompany'),
                questions: questions
            };

            // Submit to server
            const response = await fetch('/api/questions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                // Show success message
                successMessage.style.display = 'block';
                successMessage.scrollIntoView({ behavior: 'smooth' });
                
                // Reset form
                form.reset();
                
                // Reset questions container to single question
                questionsContainer.innerHTML = `
                    <div class="question-item">
                        <textarea name="question" rows="5" placeholder="Enter your question about Winplus pharmacy management platform..." required
                            class="form-control"></textarea>
                    </div>
                `;
                questionCount = 1;
                updateAddButtonVisibility();

                // Hide success message after 10 seconds
                setTimeout(() => {
                    successMessage.style.display = 'none';
                }, 10000);
            } else {
                throw new Error(result.message || 'Failed to submit questions');
            }
        } catch (error) {
            console.error('Error submitting questions:', error);
            alert('Error: ' + error.message);
        } finally {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });

    // Form validation
    const inputs = form.querySelectorAll('input[required], textarea[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('error')) {
                validateField(this);
            }
        });
    });

    function validateField(field) {
        const isValid = field.checkValidity();
        
        if (isValid) {
            field.classList.remove('error');
            field.style.borderColor = 'var(--color-primary-1)';
        } else {
            field.classList.add('error');
            field.style.borderColor = 'var(--color-primary-4)';
        }
    }

    // Email validation
    const emailInput = document.getElementById('userEmail');
    emailInput.addEventListener('input', function() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            this.setCustomValidity('Please enter a valid email address');
        } else {
            this.setCustomValidity('');
        }
    });

    // Phone number formatting (optional)
    const phoneInput = document.getElementById('userPhone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length >= 10) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        }
        this.value = value;
    });

    // Auto-resize textareas
    document.addEventListener('input', function(e) {
        if (e.target.tagName === 'TEXTAREA') {
            e.target.style.height = 'auto';
            e.target.style.height = e.target.scrollHeight + 'px';
        }
    });

    // Add loading animation to cards
    const cards = document.querySelectorAll('.card');
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
});
