<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winplus FAQ - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        secondary: {
                            500: '#8b5cf6',
                            600: '#7c3aed',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .status-badge {
            @apply px-3 py-1 rounded-full text-xs font-semibold;
        }
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-reviewed { @apply bg-blue-100 text-blue-800; }
        .status-answered { @apply bg-green-100 text-green-800; }
        .status-archived { @apply bg-gray-100 text-gray-800; }

        .admin-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .stat-card {
            background: linear-gradient(145deg, #ffffff, #f8fafc);
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-2xl mr-3"></i>
                    <div>
                        <h1 class="text-xl font-bold">Winplus FAQ Admin</h1>
                        <p class="text-sm opacity-90">Dashboard</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="exportBtn"
                        class="bg-white bg-opacity-20 hover:bg-opacity-30 px-6 py-3 rounded-xl transition-all font-medium backdrop-filter backdrop-blur-sm">
                        <i class="fas fa-download mr-2"></i>Export Excel
                    </button>
                    <button id="refreshBtn"
                        class="bg-white bg-opacity-20 hover:bg-opacity-30 px-6 py-3 rounded-xl transition-all font-medium backdrop-filter backdrop-blur-sm">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                    <button id="logoutBtn"
                        class="bg-red-500 hover:bg-red-600 px-6 py-3 rounded-xl transition-all font-medium shadow-lg">
                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center">
                    <div class="feature-icon w-12 h-12">
                        <i class="fas fa-question-circle text-lg"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm font-medium">Total Questions</p>
                        <p class="text-3xl font-bold text-gray-800" id="totalQuestions">0</p>
                    </div>
                </div>
            </div>
            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-clock text-white text-lg"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm font-medium">Pending</p>
                        <p class="text-3xl font-bold text-gray-800" id="pendingQuestions">0</p>
                    </div>
                </div>
            </div>
            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-check-circle text-white text-lg"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm font-medium">Answered</p>
                        <p class="text-3xl font-bold text-gray-800" id="answeredQuestions">0</p>
                    </div>
                </div>
            </div>
            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-users text-white text-lg"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm font-medium">Unique Users</p>
                        <p class="text-3xl font-bold text-gray-800" id="uniqueUsers">0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="admin-card rounded-xl p-8 mb-8">
            <div class="flex flex-wrap items-center gap-6">
                <div class="flex-1 min-w-64">
                    <input type="text" id="searchInput" placeholder="Search questions, names, or emails..."
                        class="form-input">
                </div>
                <select id="statusFilter" class="form-input w-auto min-w-40">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="reviewed">Reviewed</option>
                    <option value="answered">Answered</option>
                    <option value="archived">Archived</option>
                </select>
                <button id="clearFilters" class="btn-secondary">
                    <i class="fas fa-times mr-2"></i>Clear
                </button>
            </div>
        </div>

        <!-- Questions Table -->
        <div class="admin-card rounded-xl overflow-hidden">
            <div class="px-8 py-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-list mr-3 text-primary-500"></i>FAQ Questions
                </h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="questionsTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- Questions will be loaded here -->
                    </tbody>
                </table>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-12">
                <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                <p class="text-gray-600">Loading questions...</p>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="hidden text-center py-12">
                <i class="fas fa-inbox text-3xl text-gray-400 mb-4"></i>
                <p class="text-gray-600">No questions found</p>
            </div>
        </div>
    </main>

    <!-- Question Detail Modal -->
    <div id="questionModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">Question Details</h3>
                    <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div id="modalContent">
                    <!-- Modal content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
</body>
</html>
